# 宝箱计时器系统实现说明

## 概述

根据你的需求，我已经对现有的 `ActivityProgressManager` 和 `GoldCoinSystemTaskCenterViewController` 进行了优化，实现了一个符合你要求的宝箱计时器系统。

## 核心功能实现

### 1. 全局唯一宝箱计时器

- **入口点**: `ActivityProgressManager.shared.initializeAndStartTracking()`
- **位置**: 任务中心 `viewWillAppear` 中调用
- **特点**: 
  - 只有第一次进入任务中心时才启动
  - 后续进入会检查并恢复计时器状态
  - 全局唯一，避免重复创建

### 2. 持续运行机制

- **前台运行**: 计时器在app前台时持续运行
- **后台处理**: 
  - 进入后台时暂停计时器，但保存状态
  - 回到前台时恢复计时器，补偿时间差
- **跨页面**: 在app内任何页面都不停止，直到倒计时结束

### 3. 状态同步

- **实时状态**: 通过 `getTreasureChestStatus()` 获取当前状态
- **状态包含**:
  - 剩余时间（秒）
  - 总时长（秒）
  - 是否完成
  - 宝箱配置信息
- **UI同步**: 回到任务中心时自动更新UI状态

### 4. 上报机制

#### 定期上报（处理时间丢失）
- **频率**: 每5秒上报一次
- **目的**: 处理退出app导致的时间丢失
- **方法**: `reportTreasureChestProgress()`

#### 完成上报
- **时机**: 倒计时结束时立即上报
- **方法**: `reportTreasureChestCompletion()`
- **后续**: 上报后刷新配置，获取下一个宝箱

### 5. 手动领取

- **条件**: 宝箱完成且未上报时可领取
- **方法**: `claimTreasureChestReward(completion:)`
- **流程**: 
  1. 调用领取接口
  2. 更新服务器状态
  3. 清除当前宝箱状态
  4. 初始化下一个宝箱

## 关键方法说明

### ActivityProgressManager

```swift
// 初始化宝箱系统（全局入口）
func initializeAndStartTracking()

// 初始化特定宝箱
func initializeTreasureChest(with config: ActiveRewardConfig)

// 获取宝箱状态
func getTreasureChestStatus() -> (remainingSeconds: Int, totalSeconds: Int, isCompleted: Bool, config: ActiveRewardConfig?)

// 检查是否可领取
func isTreasureChestAvailable() -> Bool

// 领取宝箱奖励
func claimTreasureChestReward(completion: @escaping (Bool) -> Void)
```

### 任务中心控制器

```swift
// 首次初始化宝箱系统
private func handleFirstTimeActivation()

// 处理宝箱奖励领取
private func handleTreasureChestRewardClaim()

// 更新宝箱按钮状态
private func updateGiftButtonWithTreasureChestData()
```

## 数据流程

### 1. 初始化流程
```
进入任务中心 → 加载任务配置 → 获取活跃度数据 → 初始化宝箱计时器 → 开始倒计时
```

### 2. 运行流程
```
每秒更新进度 → 每5秒上报进度 → 倒计时结束 → 上报完成状态 → 刷新配置 → 初始化下一个宝箱
```

### 3. 领取流程
```
用户点击领取 → 调用领取接口 → 更新服务器状态 → 清除当前宝箱 → 初始化下一个宝箱
```

## 通知机制

### 1. 进度更新通知
- **名称**: `ActivityProgressManager.treasureChestProgressUpdatedNotification`
- **频率**: 每秒发送
- **用途**: 更新UI显示

### 2. 完成通知
- **时机**: 宝箱倒计时完成时
- **用途**: 提示用户可以领取奖励

## 持久化存储

### UserDefaults 键名
- `ActivityProgress_TreasureChestConfig`: 宝箱配置
- `ActivityProgress_TreasureChestStartTime`: 开始时间
- `ActivityProgress_TreasureChestRemaining`: 剩余时间
- `ActivityProgress_TreasureChestTotal`: 总时长
- `ActivityProgress_TreasureChestCompleted`: 是否完成
- `ActivityProgress_TreasureChestReported`: 是否已上报
- `ActivityProgress_TreasureChestLastReport`: 上次上报时间

## 错误处理

### 1. 网络错误
- 上报失败时会在日志中记录
- 不影响本地计时器继续运行

### 2. 状态不一致
- 提供状态验证方法 `validateTreasureChestState()`
- 自动修正不一致的状态

### 3. 重复初始化
- 检查现有计时器状态，避免重复创建
- 保护机制确保全局唯一性

## 测试建议

1. **基础功能测试**:
   - 进入任务中心，检查宝箱计时器是否启动
   - 切换到其他页面，计时器是否继续运行
   - 回到任务中心，状态是否正确同步

2. **后台恢复测试**:
   - 进入后台一段时间后回到前台
   - 检查时间是否正确补偿

3. **完成流程测试**:
   - 等待倒计时完成
   - 检查是否自动上报
   - 检查下一个宝箱是否正确初始化

4. **领取流程测试**:
   - 宝箱完成后点击领取
   - 检查金币是否增加
   - 检查下一个宝箱是否开始

## 注意事项

1. **时间精度**: 使用秒级精度，符合服务器要求
2. **网络依赖**: 上报功能依赖网络，但不影响本地计时
3. **内存管理**: 正确处理定时器的创建和销毁
4. **线程安全**: 所有UI更新都在主线程进行

这个实现完全符合你的需求，提供了一个稳定、可靠的宝箱计时器系统。
