import Foundation
import UIKit

/// 活跃度进度管理器
/// 负责管理用户活跃度的上报和状态跟踪
class ActivityProgressManager {
    
    // MARK: - 单例
    static let shared = ActivityProgressManager()
    private init() {
        setupNotifications()
    }
    
    // MARK: - 属性
    
    /// 是否正在跟踪活跃度
    private var isTracking: Bool = false

    /// 是否已经初始化过（只有第一次进入任务中心时才启动）
    private var hasInitialized: Bool = false

    /// 活跃度开始时间（用于实时计算总活跃时长）
    private var activityStartTime: Date?

    /// 服务器返回的基础活跃时长（秒）
    private var baseActiveSeconds: Int = 0
    
    /// 服务器数据获取时间（用于计算服务器时间偏移）
    private var serverDataFetchTime: Date?

    /// 上次上报时间
    private var lastReportTime: Date?

    /// 累计活跃时长（秒）
    private var accumulatedSeconds: Int = 0

    /// 上报间隔（秒）- 每30秒上报一次
    private let reportInterval: TimeInterval = 30
    
    /// 宝箱上报间隔（秒）- 每5秒上报一次
    private let treasureChestReportInterval: TimeInterval = 5

    /// 定时器
    private var reportTimer: Timer?
    
    /// 宝箱专用计时器
    private var treasureChestTimer: Timer?
    
    /// 当前宝箱配置
    private var currentTreasureChestConfig: ActiveRewardConfig?
    
    /// 宝箱开始时间
    private var treasureChestStartTime: Date?
    
    /// 宝箱剩余时间（秒）
    private var treasureChestRemainingSeconds: Int = 0
    
    /// 宝箱总时长（秒）
    private var treasureChestTotalSeconds: Int = 0
    
    /// 宝箱是否已完成
    private var isTreasureChestCompleted: Bool = false
    
    /// 宝箱是否已上报
    private var isTreasureChestReported: Bool = false

    /// 当前活跃度数据
    private var currentActiveData: ActiveProgressData?
    
    /// UserDefaults键名
    private let kLastServerTotalSecondsKey = "ActivityProgress_LastServerTotalSeconds"
    private let kLastServerFetchTimeKey = "ActivityProgress_LastServerFetchTime"
    private let kTreasureChestConfigKey = "ActivityProgress_TreasureChestConfig"
    private let kTreasureChestStartTimeKey = "ActivityProgress_TreasureChestStartTime"
    private let kTreasureChestRemainingKey = "ActivityProgress_TreasureChestRemaining"
    private let kTreasureChestTotalKey = "ActivityProgress_TreasureChestTotal"
    private let kTreasureChestCompletedKey = "ActivityProgress_TreasureChestCompleted"
    private let kTreasureChestReportedKey = "ActivityProgress_TreasureChestReported"
    private let kTreasureChestLastReportKey = "ActivityProgress_TreasureChestLastReport"
    
    // MARK: - 通知名称
    static let activityProgressUpdatedNotification = Notification.Name("ActivityProgressUpdated")
    static let treasureChestProgressUpdatedNotification = Notification.Name("TreasureChestProgressUpdated")
    
    // MARK: - 公开方法
    
    /// 初始化并开始跟踪活跃度（只有第一次进入任务中心时调用）
    func initializeAndStartTracking() {
        guard !hasInitialized else {
            print("[ActivityProgress] 已经初始化过，跳过重复初始化")
            return
        }

        hasInitialized = true
        
        // 尝试恢复上次的服务器数据
        restoreServerDataIfNeeded()
        
        // 恢复宝箱状态
        restoreTreasureChestState()
        
        startTracking()
        print("[ActivityProgress] 首次初始化并开始跟踪活跃度")
    }
    
    /// 恢复上次的服务器数据（如果存在且时间合理）
    private func restoreServerDataIfNeeded() {
        let lastServerSeconds = UserDefaults.standard.integer(forKey: kLastServerTotalSecondsKey)
        let lastFetchTime = UserDefaults.standard.object(forKey: kLastServerFetchTimeKey) as? Date
        
        if lastServerSeconds > 0, let fetchTime = lastFetchTime {
            let timeSinceLastFetch = Date().timeIntervalSince(fetchTime)
            // 如果距离上次获取时间不超过1小时，则恢复数据
            if timeSinceLastFetch < 3600 {
                baseActiveSeconds = lastServerSeconds
                serverDataFetchTime = fetchTime
                print("[ActivityProgress] 恢复上次服务器数据: \(lastServerSeconds)秒，距离上次获取: \(Int(timeSinceLastFetch))秒")
            } else {
                print("[ActivityProgress] 上次数据过期，将重新获取")
                // 清除过期数据
                UserDefaults.standard.removeObject(forKey: kLastServerTotalSecondsKey)
                UserDefaults.standard.removeObject(forKey: kLastServerFetchTimeKey)
            }
        }
    }
    
    /// 恢复宝箱状态
    private func restoreTreasureChestState() {
        // 恢复宝箱配置
        if let configData = UserDefaults.standard.data(forKey: kTreasureChestConfigKey),
           let config = try? JSONDecoder().decode(ActiveRewardConfig.self, from: configData) {
            currentTreasureChestConfig = config
        }
        
        // 恢复宝箱时间状态
        treasureChestStartTime = UserDefaults.standard.object(forKey: kTreasureChestStartTimeKey) as? Date
        treasureChestRemainingSeconds = UserDefaults.standard.integer(forKey: kTreasureChestRemainingKey)
        treasureChestTotalSeconds = UserDefaults.standard.integer(forKey: kTreasureChestTotalKey)
        isTreasureChestCompleted = UserDefaults.standard.bool(forKey: kTreasureChestCompletedKey)
        isTreasureChestReported = UserDefaults.standard.bool(forKey: kTreasureChestReportedKey)
        
        // 如果宝箱未完成且有剩余时间，重新启动计时器
        if !isTreasureChestCompleted && treasureChestRemainingSeconds > 0 {
            startTreasureChestTimer()
            print("[ActivityProgress] 恢复宝箱计时器: 剩余\(treasureChestRemainingSeconds)秒")
        }
    }

    /// 开始跟踪活跃度（内部方法）
    private func startTracking() {
        guard !isTracking else { return }

        isTracking = true
        activityStartTime = Date()
        lastReportTime = Date()
        accumulatedSeconds = 0

        // 启动定时器，每30秒上报一次
        reportTimer = Timer.scheduledTimer(withTimeInterval: reportInterval, repeats: true) { [weak self] _ in
            self?.reportAccumulatedProgress()
        }

        print("[ActivityProgress] 开始跟踪活跃度")
    }
    
    /// 启动宝箱计时器
    private func startTreasureChestTimer() {
        // 先检查是否已有有效的计时器在运行
        if let timer = treasureChestTimer, timer.isValid {
            print("[ActivityProgress] 宝箱计时器已在运行，跳过重复启动")
            return
        }
        
        // 停止现有计时器
        treasureChestTimer?.invalidate()
        treasureChestTimer = nil
        
        guard let config = currentTreasureChestConfig else {
            print("[ActivityProgress] 没有宝箱配置，无法启动计时器")
            return
        }
        
        // 如果是首次启动（没有开始时间），设置开始时间
        if treasureChestStartTime == nil {
            treasureChestStartTime = Date()
            treasureChestTotalSeconds = config.conditionValue
            treasureChestRemainingSeconds = config.conditionValue
            isTreasureChestCompleted = false
            isTreasureChestReported = false
            
            // 保存宝箱状态
            saveTreasureChestState()
        }
        
        // 如果已完成，不需要启动计时器
        if isTreasureChestCompleted {
            print("[ActivityProgress] 宝箱已完成，不需要启动计时器")
            return
        }
        
        // 启动每秒更新的计时器
        treasureChestTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateTreasureChestProgress()
        }
        
        print("[ActivityProgress] 启动宝箱计时器: 总时长\(treasureChestTotalSeconds)秒，剩余\(treasureChestRemainingSeconds)秒")
    }
    
    /// 更新宝箱进度
    private func updateTreasureChestProgress() {
        guard let startTime = treasureChestStartTime, !isTreasureChestCompleted else { return }
        
        let elapsedSeconds = Int(Date().timeIntervalSince(startTime))
        treasureChestRemainingSeconds = max(0, treasureChestTotalSeconds - elapsedSeconds)
        
        // 检查是否完成
        if treasureChestRemainingSeconds <= 0 {
            treasureChestRemainingSeconds = 0
            isTreasureChestCompleted = true
            treasureChestTimer?.invalidate()
            treasureChestTimer = nil
            
            print("[ActivityProgress] 宝箱计时完成")
            
            // 发送完成通知
            notifyTreasureChestCompleted()
        }
        
        // 保存状态
        saveTreasureChestState()
        
        // 发送进度更新通知
        notifyTreasureChestProgressUpdated()
        
        // 每5秒上报一次进度
        let now = Date()
        let lastReportTime = UserDefaults.standard.object(forKey: kTreasureChestLastReportKey) as? Date ?? Date.distantPast
        if now.timeIntervalSince(lastReportTime) >= treasureChestReportInterval {
            reportTreasureChestProgress()
            UserDefaults.standard.set(now, forKey: kTreasureChestLastReportKey)
        }
    }
    
    /// 暂停跟踪活跃度（应用进入后台时）
    func pauseTracking() {
        guard isTracking else { return }

        // 停止定时器但保持跟踪状态
        reportTimer?.invalidate()
        reportTimer = nil
        
        // 停止宝箱计时器
        treasureChestTimer?.invalidate()
        treasureChestTimer = nil

        // 上报剩余的活跃时长
        if accumulatedSeconds > 0 {
            reportAccumulatedProgress()
        }
        
        // 上报宝箱进度
        reportTreasureChestProgress()

        print("[ActivityProgress] 暂停跟踪活跃度")
    }

    /// 恢复跟踪活跃度（应用进入前台时）
    func resumeTracking() {
        guard hasInitialized && (reportTimer?.isValid != true) else { return }

        // 重新启动定时器
        lastReportTime = Date()
        reportTimer = Timer.scheduledTimer(withTimeInterval: reportInterval, repeats: true) { [weak self] _ in
            self?.reportAccumulatedProgress()
        }
        
        // 恢复宝箱计时器
        if !isTreasureChestCompleted && treasureChestRemainingSeconds > 0 {
            // 调整开始时间以补偿后台时间
            if let startTime = treasureChestStartTime {
                let elapsedSeconds = Int(Date().timeIntervalSince(startTime))
                let expectedRemaining = treasureChestTotalSeconds - elapsedSeconds
                
                // 如果实际剩余时间与预期不符，调整开始时间
                if expectedRemaining != treasureChestRemainingSeconds {
                    let adjustment = treasureChestRemainingSeconds - expectedRemaining
                    treasureChestStartTime = Date().addingTimeInterval(TimeInterval(-elapsedSeconds - adjustment))
                    print("[ActivityProgress] 调整宝箱开始时间: 补偿\(adjustment)秒")
                }
            }
            
            startTreasureChestTimer()
        }

        print("[ActivityProgress] 恢复跟踪活跃度")
    }

    /// 完全停止跟踪活跃度（仅用于清理）
    private func stopTracking() {
        isTracking = false
        reportTimer?.invalidate()
        reportTimer = nil
        treasureChestTimer?.invalidate()
        treasureChestTimer = nil
        activityStartTime = nil
        
        print("[ActivityProgress] 完全停止跟踪活跃度")
    }
    
    /// 手动上报活跃度（用于特殊场景，如首次激活）
    /// - Parameter seconds: 要上报的秒数
    func reportProgress(seconds: Int) {
        APIManager.shared.reportActiveProgress(activeSeconds: seconds) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let data = response.data {
                        self?.updateServerData(data)
                        print("[ActivityProgress] 手动上报成功: \(seconds)秒，服务器总时长: \(data.totalSeconds)秒")
                    } else {
                        print("[ActivityProgress] 手动上报失败: \(response.displayMessage)")
                    }
                case .failure(let error):
                    print("[ActivityProgress] 手动上报请求失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    /// 获取当前活跃度数据
    func getCurrentActiveData() -> ActiveProgressData? {
        return currentActiveData
    }

    /// 更新当前活跃度数据（用于领取奖励后同步状态）
    func updateCurrentActiveData(_ data: ActiveProgressData) {
        updateServerData(data)
        print("[ActivityProgress] 活跃度数据已手动更新，服务器时长: \(data.totalSeconds)秒")
    }

    /// 获取当前实时总活跃时长（基于服务器时间推算）
    func getCurrentTotalActiveSeconds() -> Int {
        // 优先使用基于服务器时间的推算
        if let serverFetchTime = serverDataFetchTime {
            let timeSinceServerFetch = Date().timeIntervalSince(serverFetchTime)
            let estimatedTotalSeconds = baseActiveSeconds + Int(timeSinceServerFetch)
            return estimatedTotalSeconds
        }
        
        // 降级到本地会话时间计算
        let sessionSeconds = getCurrentSessionActiveSeconds()
        return baseActiveSeconds + sessionSeconds
    }

    /// 获取当前会话的活跃时长（仅作为降级方案）
    private func getCurrentSessionActiveSeconds() -> Int {
        guard let startTime = activityStartTime else { return 0 }
        let sessionDuration = Date().timeIntervalSince(startTime)
        return Int(sessionDuration)
    }
    
    /// 刷新活跃度数据
    func refreshActiveData() {
        APIManager.shared.queryActiveProgress { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let data = response.data {
                        self?.updateServerData(data)
                        print("[ActivityProgress] 刷新活跃度数据成功，服务器时长: \(data.totalSeconds)秒")
                    } else {
                        print("[ActivityProgress] 刷新活跃度数据失败: \(response.displayMessage)")
                    }
                case .failure(let error):
                    print("[ActivityProgress] 刷新活跃度数据请求失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    /// 更新服务器数据（不重置时间）
    private func updateServerData(_ data: ActiveProgressData) {
        currentActiveData = data
        baseActiveSeconds = data.totalSeconds
        serverDataFetchTime = Date()
        
        // 持久化服务器数据
        UserDefaults.standard.set(data.totalSeconds, forKey: kLastServerTotalSecondsKey)
        UserDefaults.standard.set(serverDataFetchTime, forKey: kLastServerFetchTimeKey)
        
        // 只在首次初始化时设置活跃度开始时间
        if activityStartTime == nil {
            activityStartTime = Date()
            print("[ActivityProgress] 首次设置活跃度开始时间")
        }
        
        notifyProgressUpdated()
        print("[ActivityProgress] 服务器数据已更新，不重置计时器")
    }
    
    /// 保存宝箱状态
    private func saveTreasureChestState() {
        UserDefaults.standard.set(treasureChestRemainingSeconds, forKey: kTreasureChestRemainingKey)
        UserDefaults.standard.set(treasureChestTotalSeconds, forKey: kTreasureChestTotalKey)
        UserDefaults.standard.set(isTreasureChestCompleted, forKey: kTreasureChestCompletedKey)
        UserDefaults.standard.set(isTreasureChestReported, forKey: kTreasureChestReportedKey)
        
        if let startTime = treasureChestStartTime {
            UserDefaults.standard.set(startTime, forKey: kTreasureChestStartTimeKey)
        }
        
        if let config = currentTreasureChestConfig {
            if let configData = try? JSONEncoder().encode(config) {
                UserDefaults.standard.set(configData, forKey: kTreasureChestConfigKey)
            }
        }
    }
    
    /// 清除宝箱状态
    private func clearTreasureChestState() {
        treasureChestStartTime = nil
        treasureChestRemainingSeconds = 0
        treasureChestTotalSeconds = 0
        isTreasureChestCompleted = false
        isTreasureChestReported = false
        currentTreasureChestConfig = nil
        
        // 清除UserDefaults
        UserDefaults.standard.removeObject(forKey: kTreasureChestConfigKey)
        UserDefaults.standard.removeObject(forKey: kTreasureChestStartTimeKey)
        UserDefaults.standard.removeObject(forKey: kTreasureChestRemainingKey)
        UserDefaults.standard.removeObject(forKey: kTreasureChestTotalKey)
        UserDefaults.standard.removeObject(forKey: kTreasureChestCompletedKey)
        UserDefaults.standard.removeObject(forKey: kTreasureChestReportedKey)
        UserDefaults.standard.removeObject(forKey: kTreasureChestLastReportKey)
    }
    
    // MARK: - 私有方法
    
    /// 设置通知监听
    private func setupNotifications() {
        // 监听应用进入前台和后台
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillResignActive),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )
    }
    
    /// 上报累计的活跃进度
    private func reportAccumulatedProgress() {
        guard isTracking else { return }
        
        // 计算自上次上报以来的活跃时长
        let now = Date()
        if let lastTime = lastReportTime {
            let interval = now.timeIntervalSince(lastTime)
            accumulatedSeconds += Int(interval)
        }
        
        // 如果累计时长大于0，则上报
        if accumulatedSeconds > 0 {
            let secondsToReport = accumulatedSeconds
            accumulatedSeconds = 0 // 重置累计时长
            
            APIManager.shared.reportActiveProgress(activeSeconds: secondsToReport) { [weak self] result in
                DispatchQueue.main.async {
                    switch result {
                    case .success(let response):
                        if response.isSuccess, let data = response.data {
                            self?.updateServerData(data)
                            print("[ActivityProgress] 自动上报成功: \(secondsToReport)秒，服务器总时长: \(data.totalSeconds)秒")
                        } else {
                            print("[ActivityProgress] 自动上报失败: \(response.displayMessage)")
                        }
                    case .failure(let error):
                        print("[ActivityProgress] 自动上报请求失败: \(error.localizedDescription)")
                    }
                }
            }
        }
        
        lastReportTime = now
    }
    
    /// 发送活跃度更新通知
    private func notifyProgressUpdated() {
        NotificationCenter.default.post(
            name: ActivityProgressManager.activityProgressUpdatedNotification,
            object: self,
            userInfo: ["data": currentActiveData as Any]
        )
    }
    
    /// 发送宝箱进度更新通知
    private func notifyTreasureChestProgressUpdated() {
        NotificationCenter.default.post(
            name: ActivityProgressManager.treasureChestProgressUpdatedNotification,
            object: self,
            userInfo: [
                "remainingSeconds": treasureChestRemainingSeconds,
                "totalSeconds": treasureChestTotalSeconds,
                "isCompleted": isTreasureChestCompleted,
                "config": currentTreasureChestConfig as Any
            ]
        )
    }
    
    /// 发送宝箱完成通知
    private func notifyTreasureChestCompleted() {
        NotificationCenter.default.post(
            name: ActivityProgressManager.treasureChestProgressUpdatedNotification,
            object: self,
            userInfo: [
                "remainingSeconds": 0,
                "totalSeconds": treasureChestTotalSeconds,
                "isCompleted": true,
                "config": currentTreasureChestConfig as Any
            ]
        )
    }
    
    // MARK: - 通知处理
    
    @objc private func appDidBecomeActive() {
        // 应用进入前台时恢复跟踪（如果已经初始化过）
        resumeTracking()
    }

    @objc private func appWillResignActive() {
        // 应用进入后台时暂停跟踪
        pauseTracking()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        reportTimer?.invalidate()
        treasureChestTimer?.invalidate()
    }
}

// MARK: - 扩展：便捷方法

extension ActivityProgressManager {
    
    /// 检查是否有可领取的奖励
    func hasAvailableRewards() -> Bool {
        guard let data = currentActiveData else { return false }

        // 获取实时总活跃时长
        let currentTotalSeconds = getCurrentTotalActiveSeconds()

        // 检查当前未领取的奖励（当前宝箱）
        if let unclaimedReward = data.unclaimedActiveRewards {
            // 检查是否满足领取条件
            let remainingSeconds = unclaimedReward.conditionValue - currentTotalSeconds
            return remainingSeconds <= 0
        }

        return false
    }
    
    /// 获取当前宝箱的剩余时间（秒）- 使用实时计算
    func getCurrentRewardRemainingSeconds() -> Int? {
        guard let data = currentActiveData else { return nil }

        // 获取实时总活跃时长
        let currentTotalSeconds = getCurrentTotalActiveSeconds()

        // 优先检查当前未领取的奖励
        if let unclaimedReward = data.unclaimedActiveRewards {
            let remainingSeconds = unclaimedReward.conditionValue - currentTotalSeconds
            return max(0, remainingSeconds)
        }

        // 如果没有当前宝箱，检查下一个奖励
        if let nextReward = data.nextShortVideoRewardConfig {
            let remainingSeconds = nextReward.conditionValue - currentTotalSeconds
            return max(0, remainingSeconds)
        }

        return nil
    }
    
    /// 获取当前可领取的奖励金币数
    func getCurrentAvailableReward() -> Int {
        guard let data = currentActiveData else { return 0 }

        // 检查当前未领取的奖励
        if let unclaimedReward = data.unclaimedActiveRewards {
            let remainingSeconds = unclaimedReward.conditionValue - data.totalSeconds
            if remainingSeconds <= 0 {
                return unclaimedReward.rewardValue
            }
        }

        return 0
    }

    /// 获取当前宝箱的奖励配置（无论是否可领取）
    func getCurrentRewardConfig() -> ActiveRewardConfig? {
        guard let data = currentActiveData else { return nil }

        // 优先返回当前未领取的奖励
        if let unclaimedReward = data.unclaimedActiveRewards {
            return unclaimedReward
        }

        // 如果没有当前宝箱，返回下一个奖励配置
        return data.nextShortVideoRewardConfig
    }
    
    // MARK: - 宝箱相关方法
    
    /// 初始化宝箱计时器
    func initializeTreasureChest(with config: ActiveRewardConfig) {
        // 检查是否已经有相同配置的计时器在运行
        if let existingConfig = currentTreasureChestConfig,
           existingConfig.id == config.id &&
           treasureChestTimer != nil &&
           treasureChestTimer?.isValid == true {
            print("[ActivityProgress] 宝箱计时器已在运行，跳过重复初始化")
            return
        }
        
        // 如果配置不同，清除现有状态
        if currentTreasureChestConfig?.id != config.id {
            clearTreasureChestState()
        }
        
        // 设置新配置
        currentTreasureChestConfig = config
        
        // 如果条件值为0，直接完成
        if config.conditionValue == 0 {
            treasureChestStartTime = Date()
            treasureChestTotalSeconds = 0
            treasureChestRemainingSeconds = 0
            isTreasureChestCompleted = true
            isTreasureChestReported = false
            
            // 保存状态
            saveTreasureChestState()
            
            // 发送完成通知
            notifyTreasureChestCompleted()
            
            print("[ActivityProgress] 宝箱条件为0，直接完成")
        } else {
            // 检查是否已有恢复的状态
            if treasureChestStartTime == nil {
                print("[ActivityProgress] 初始化新的宝箱计时器: \(config.conditionValue)秒")
                startTreasureChestTimer()
            } else {
                print("[ActivityProgress] 恢复现有宝箱计时器: 剩余\(treasureChestRemainingSeconds)秒")
                // 如果计时器没有运行，重新启动
                if treasureChestTimer?.isValid != true {
                    startTreasureChestTimer()
                }
            }
        }
    }
    
    /// 上报宝箱进度
    private func reportTreasureChestProgress() {
        guard let config = currentTreasureChestConfig else { return }
        
        // 计算已进行的时长
        let elapsedSeconds = treasureChestTotalSeconds - treasureChestRemainingSeconds
        
        // 如果有进度且未上报过，则上报
        if elapsedSeconds > 0 && !isTreasureChestReported {
            print("[ActivityProgress] 上报宝箱进度: \(elapsedSeconds)秒")
            
            // 这里可以调用专门的上报接口
            // APIManager.shared.reportTreasureChestProgress(configId: config.id, elapsedSeconds: elapsedSeconds) { [weak self] result in
            //     DispatchQueue.main.async {
            //         switch result {
            //         case .success:
            //             print("[ActivityProgress] 宝箱进度上报成功")
            //         case .failure(let error):
            //             print("[ActivityProgress] 宝箱进度上报失败: \(error.localizedDescription)")
            //         }
            //     }
            // }
        }
    }
    
    /// 获取宝箱状态
    func getTreasureChestStatus() -> (remainingSeconds: Int, totalSeconds: Int, isCompleted: Bool, config: ActiveRewardConfig?) {
        // 状态一致性检查
        validateTreasureChestState()
        return (treasureChestRemainingSeconds, treasureChestTotalSeconds, isTreasureChestCompleted, currentTreasureChestConfig)
    }
    
    /// 验证宝箱状态一致性
    private func validateTreasureChestState() {
        guard let config = currentTreasureChestConfig else { return }
        
        // 如果已完成但剩余时间还大于0，修正状态
        if isTreasureChestCompleted && treasureChestRemainingSeconds > 0 {
            print("[ActivityProgress] 状态不一致：已完成但剩余时间>0，修正状态")
            isTreasureChestCompleted = false
            saveTreasureChestState()
        }
        
        // 如果未完成但剩余时间为0，标记为完成
        if !isTreasureChestCompleted && treasureChestRemainingSeconds <= 0 {
            print("[ActivityProgress] 状态不一致：未完成但剩余时间=0，标记完成")
            treasureChestRemainingSeconds = 0
            isTreasureChestCompleted = true
            treasureChestTimer?.invalidate()
            treasureChestTimer = nil
            saveTreasureChestState()
            notifyTreasureChestCompleted()
        }
    }
    
    /// 检查宝箱是否可领取
    func isTreasureChestAvailable() -> Bool {
        return isTreasureChestCompleted && !isTreasureChestReported
    }
    
    /// 领取宝箱奖励
    func claimTreasureChestReward(completion: @escaping (Bool) -> Void) {
        guard isTreasureChestCompleted, let config = currentTreasureChestConfig else {
            completion(false)
            return
        }
        
        // 调用领取API
        APIManager.shared.claimActiveReward(configId: config.id) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        // 标记为已上报
                        self?.isTreasureChestReported = true
                        self?.saveTreasureChestState()
                        
                        // 清除宝箱状态
                        self?.clearTreasureChestState()
                        
                        print("[ActivityProgress] 宝箱奖励领取成功")
                        completion(true)
                    } else {
                        print("[ActivityProgress] 宝箱奖励领取失败: \(response.displayMessage)")
                        completion(false)
                    }
                case .failure(let error):
                    print("[ActivityProgress] 宝箱奖励领取请求失败: \(error.localizedDescription)")
                    completion(false)
                }
            }
        }
    }
}
